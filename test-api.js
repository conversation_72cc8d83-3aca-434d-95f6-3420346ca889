// Simple test to verify Gemini API connection
const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testGeminiAPI() {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENAI_API_KEY || 'AIzaSyBKgNP6SsXTUMnUfEwsJdtxtTefJQkxs_E');
    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

    const prompt = "Say hello in one sentence.";
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ API Test Successful!');
    console.log('Response:', text);
  } catch (error) {
    console.error('❌ API Test Failed:');
    console.error('Error:', error.message);
    console.error('Full error:', error);
  }
}

testGeminiAPI();
