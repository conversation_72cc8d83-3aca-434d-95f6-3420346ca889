import { initialPatientMessageFlow, ai } from './genkit';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { message, disease, gameContext, isInitialMessage } = await req.json();

    // If this is a request for initial patient message, return non-streaming response
    if (isInitialMessage) {
      const response = await initialPatientMessageFlow({ disease });
      return NextResponse.json({ response });
    }

    // For normal conversation, use streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Create the prompt for the patient
          const prompt = `You are a patient with ${disease}. The doctor is trying to diagnose you in a medical guessing game.

Rules:
- Answer questions about symptoms honestly based on ${disease}
- If asked about tests/examinations, provide realistic results for ${disease}
- Keep responses SHORT (1-2 sentences max)
- Don't reveal the disease name directly
- Act like a real patient would - sometimes uncertain, describing symptoms in everyday language
- If the doctor asks for specific tests, provide results that would be consistent with ${disease}

Game context: ${gameContext}
Doctor's question: "${message}"

Respond as the patient:`

          // Use ai.generateStream directly
          const { stream, response } = ai.generateStream({
            model: 'googleai/gemini-2.0-flash-exp',
            prompt: prompt,
            config: {
              maxOutputTokens: 100,
              temperature: 0.7,
            },
          });

          // Process the stream
          for await (const chunk of stream) {
            if (chunk.text) {
              // Send each chunk as it's generated
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content: chunk.text })}\n\n`));
            }
          }

          // Send end signal
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();
        } catch (error) {
          console.error('Error during streaming:', error);
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: 'Failed to generate response' })}\n\n`));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error("Error generating AI response:", error);
    return NextResponse.json(
      {
        error: "Failed to generate response",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
