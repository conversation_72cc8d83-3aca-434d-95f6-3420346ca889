import { menuSuggestionFlow, initialPatientMessageFlow } from './genkit';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { message, disease, gameContext, isInitialMessage } = await req.json();

    // If this is a request for initial patient message, return non-streaming response
    if (isInitialMessage) {
      const response = await initialPatientMessageFlow({ disease });
      return NextResponse.json({ response });
    }

    // For normal conversation, use streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Use the streaming flow with the .stream() method
          const response = menuSuggestionFlow.stream({ message, disease, gameContext });
          
          // Process the stream
          for await (const chunk of response.stream) {
            // Send each chunk as it's generated
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content: chunk })}\n\n`));
          }
          
          // Send end signal
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();
        } catch (error) {
          console.error('Error during streaming:', error);
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: 'Failed to generate response' })}\n\n`));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error("Error generating AI response:", error);
    return NextResponse.json(
      {
        error: "Failed to generate response",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
