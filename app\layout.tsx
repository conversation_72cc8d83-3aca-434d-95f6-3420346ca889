import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Guess Disease - AI Medical Diagnosis Game | Practice Medical Skills Online",
  description:
    "Test your medical diagnostic skills with our Google AI-powered patient simulator. Interactive medical training game with 20+ diseases, realistic symptoms, and clinical test results. Perfect for medical students and healthcare professionals.",
  keywords:
    "medical diagnosis game, AI patient simulator, medical training, healthcare education, diagnostic skills, medical students, clinical practice, disease diagnosis, medical learning game",
  authors: [{ name: "Guess Disease Game" }],
  creator: "Guess Disease Game",
  publisher: "Guess Disease Game",
  robots: "index, follow",
  openGraph: {
    title: "Guess Disease - AI Medical Diagnosis Game",
    description:
      "Practice medical diagnosis with our AI patient simulator. Interactive training for medical professionals and students.",
    type: "website",
    locale: "en_US",
    siteName: "Guess Disease Game",
  },
  twitter: {
    card: "summary_large_image",
    title: "Guess Disease - AI Medical Diagnosis Game",
    description:
      "Practice medical diagnosis with our AI patient simulator. Interactive training for medical professionals and students.",
  },
  alternates: {
    canonical: "https://guess-disease.com",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={inter.className}>{children}</body>
    </html>
  )
}
