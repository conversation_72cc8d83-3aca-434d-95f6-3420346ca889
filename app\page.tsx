"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Stethoscope, MessageSquare, RotateCcw, Trophy, Brain, Send } from "lucide-react"

interface Message {
  id: string
  role: "user" | "patient"
  content: string
  timestamp: Date
}

interface GameState {
  isActive: boolean
  currentRound: number
  maxRounds: number
  disease: string
  messages: Message[]
  gameWon: boolean
  gameOver: boolean
}

const diseases = [
  "Pneumonia",
  "Diabetes Type 2",
  "Hypertension",
  "Migraine",
  "Asthma",
  "Gastroenteritis",
  "Urinary Tract Infection",
  "Bronchitis",
  "Anxiety Disorder",
  "Appendicitis",
  "Influenza",
  "Strep Throat",
  "Food Poisoning",
  "Allergic Rhinitis",
  "Osteoarthritis",
  "Depression",
  "Acid Reflux",
  "Sinusitis",
  "Anemia",
  "Hypothyroidism",
  "Coronary Artery Disease",
  "Chronic Kidney Disease",
  "Rheumatoid Arthritis",
  "Peptic Ulcer Disease",
  "Atrial Fibrillation",
]

export default function GuessDiseasePage() {
  const [gameState, setGameState] = useState<GameState>({
    isActive: false,
    currentRound: 0,
    maxRounds: 20,
    disease: "",
    messages: [],
    gameWon: false,
    gameOver: false,
  })

  const [inputMessage, setInputMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isStartingGame, setIsStartingGame] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: "smooth",
      block: "nearest",
      inline: "nearest"
    })
  }

  // 检查用户是否接近聊天底部
  const isNearBottom = () => {
    if (!scrollAreaRef.current) return true
    const element = scrollAreaRef.current
    const threshold = 100 // 距离底部100px内认为是接近底部
    return element.scrollHeight - element.scrollTop - element.clientHeight <= threshold
  }

  // 只在用户接近底部时才自动滚动
  useEffect(() => {
    if (isNearBottom()) {
      scrollToBottom()
    }
  }, [gameState.messages])

  // 自动开始游戏
  useEffect(() => {
    startNewGame()
  }, [])

  const startNewGame = async () => {
    setIsStartingGame(true)
    const randomDisease = diseases[Math.floor(Math.random() * diseases.length)]
    
    try {
      // Call AI to generate initial patient message based on the disease
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          disease: randomDisease,
          isInitialMessage: true,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to generate initial message")
      }

      const data = await response.json()

      setGameState({
        isActive: true,
        currentRound: 0,
        maxRounds: 20,
        disease: randomDisease,
        messages: [
          {
            id: "1",
            role: "patient",
            content: data.response,
            timestamp: new Date(),
          },
        ],
        gameWon: false,
        gameOver: false,
      })
    } catch (error) {
      console.error("Error generating initial message:", error)
      // Fallback to generic message if AI fails
      setGameState({
        isActive: true,
        currentRound: 0,
        maxRounds: 20,
        disease: randomDisease,
        messages: [
          {
            id: "1",
            role: "patient",
            content: "Hello doctor, I'm not feeling well lately. I've been experiencing some symptoms that are concerning me. Could you help me figure out what might be wrong?",
            timestamp: new Date(),
          },
        ],
        gameWon: false,
        gameOver: false,
      })
    } finally {
      setIsStartingGame(false)
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || !gameState.isActive || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage,
      timestamp: new Date(),
    }

    const newRound = gameState.currentRound + 1

    // Check if user is making a diagnosis guess
    const isGuess =
      inputMessage.toLowerCase().includes("diagnose") ||
      inputMessage.toLowerCase().includes("disease") ||
      inputMessage.toLowerCase().includes("condition") ||
      diseases.some((disease) => inputMessage.toLowerCase().includes(disease.toLowerCase()))

    if (isGuess && inputMessage.toLowerCase().includes(gameState.disease.toLowerCase())) {
      setGameState((prev) => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        gameWon: true,
        gameOver: true,
        isActive: false,
      }))
      setInputMessage("")
      return
    }

    setGameState((prev) => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      currentRound: newRound,
    }))

    setInputMessage("")
    setIsLoading(true)

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: inputMessage,
          disease: gameState.disease,
          gameContext: `Round ${newRound}/${gameState.maxRounds}`,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to get AI response")
      }

      const data = await response.json()

      const patientMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "patient",
        content: data.response,
        timestamp: new Date(),
      }

      setGameState((prev) => {
        const isGameOver = newRound >= prev.maxRounds
        return {
          ...prev,
          messages: [...prev.messages, patientMessage],
          gameOver: isGameOver,
          isActive: !isGameOver,
        }
      })
    } catch (error) {
      console.error("Error generating response:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "patient",
        content: "I'm sorry, I'm having trouble responding right now. Please try again.",
        timestamp: new Date(),
      }

      setGameState((prev) => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
      }))
    }

    setIsLoading(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Modern Header */}
      <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-blue-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Stethoscope className="h-6 w-6 text-white" />
              </div>
              <div className="p-1.5 bg-green-500 rounded-lg">
                <Brain className="h-5 w-5 text-white" />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Medical Diagnosis Game</h1>
              <p className="text-gray-600 text-sm">
                Test your diagnostic skills with our intelligent AI patient
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {/* Game Instructions */}
        <section className="mb-6">
          <div className="max-w-5xl mx-auto grid md:grid-cols-3 gap-4">
            <Card className="border-blue-200 bg-white/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg">Ask Questions</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Interview the AI patient about their symptoms and medical history
                </p>
              </CardContent>
            </Card>
            <Card className="border-green-200 bg-white/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-green-600" />
                  <CardTitle className="text-lg">Order Tests</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Request medical examinations to gather diagnostic information
                </p>
              </CardContent>
            </Card>
            <Card className="border-yellow-200 bg-white/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-600" />
                  <CardTitle className="text-lg">Make Diagnosis</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Correctly identify the disease within 20 questions
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Main Chat Interface */}
        <div className="max-w-5xl mx-auto">
          <Card className="h-[70vh] flex flex-col bg-white/80 backdrop-blur-sm border-slate-200 shadow-xl">
            <CardHeader className="flex-shrink-0 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Stethoscope className="h-5 w-5 text-blue-600" />
                      <span className="text-gray-800">Medical Consultation</span>
                    </div>
                    <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 border-blue-200">
                      AI Powered
                    </Badge>
                  </CardTitle>
                  <CardDescription className="text-gray-600 mt-1">
                    {gameState.isActive
                      ? `Round ${gameState.currentRound}/${gameState.maxRounds} - Ask questions to diagnose the patient`
                      : "Ready to start a new diagnostic challenge"}
                  </CardDescription>
                </div>
                <div className="flex gap-3 items-center">
                  {gameState.isActive && (
                    <Badge variant="outline" className="text-sm bg-white/80 border-blue-200">
                      {gameState.maxRounds - gameState.currentRound} questions left
                    </Badge>
                  )}
                  <Button onClick={startNewGame} variant="outline" size="sm" className="bg-white/80">
                    <RotateCcw className="h-4 w-4 mr-1" />
                    New Game
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col p-0">
              <ScrollArea className="flex-1 p-6" ref={scrollAreaRef}>
                <div className="space-y-4">
                  {gameState.messages.map((message, index) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} animate-in slide-in-from-bottom-2 duration-300`}
                      style={{animationDelay: `${index * 100}ms`}}
                    >
                      <div className="flex items-start gap-3 max-w-[85%]">
                        {message.role === "patient" && (
                          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center">
                            <Brain className="h-4 w-4 text-white" />
                          </div>
                        )}
                        <div
                          className={`rounded-2xl px-4 py-3 shadow-sm ${
                            message.role === "user" 
                              ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto" 
                              : "bg-white border border-gray-200"
                          }`}
                        >
                          <p className={`text-sm leading-relaxed ${message.role === "user" ? "text-white" : "text-gray-800"}`}>
                            {message.content}
                          </p>
                          <p className={`text-xs mt-2 ${message.role === "user" ? "text-blue-100" : "text-gray-500"}`}>
                            {message.role === "user" ? "Doctor" : "AI Patient"} • {message.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                        {message.role === "user" && (
                          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <Stethoscope className="h-4 w-4 text-white" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start animate-in slide-in-from-bottom-2">
                      <div className="flex items-start gap-3 max-w-[85%]">
                        <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center">
                          <Brain className="h-4 w-4 text-white animate-pulse" />
                        </div>
                        <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                          <div className="flex items-center gap-2">
                            <div className="flex gap-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                            </div>
                            <p className="text-sm text-gray-600">AI patient is thinking...</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Game Result Messages */}
              {gameState.gameWon && (
                <div className="mx-6 mb-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                  <div className="flex items-center gap-2 text-green-800">
                    <Trophy className="h-5 w-5" />
                    <span className="font-semibold">Congratulations! You correctly diagnosed: {gameState.disease}</span>
                  </div>
                </div>
              )}

              {gameState.gameOver && !gameState.gameWon && (
                <div className="mx-6 mb-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl">
                  <div className="flex items-center gap-2 text-red-800">
                    <AlertCircle className="h-5 w-5" />
                    <span className="font-semibold">Game Over! The correct diagnosis was: {gameState.disease}</span>
                  </div>
                </div>
              )}

              {/* Input Area */}
              {gameState.isActive && (
                <div className="p-6 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50">
                  <div className="flex gap-3">
                    <Input
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Ask about symptoms, request tests, or make your diagnosis..."
                      disabled={isLoading}
                      className="flex-1 border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                    />
                    <Button 
                      onClick={sendMessage} 
                      disabled={!inputMessage.trim() || isLoading}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    💡 Tip: Ask about specific symptoms, request lab tests, or make your final diagnosis
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* About Section */}
        <section className="mt-12 max-w-5xl mx-auto">
          <Card className="bg-white/60 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <h2 className="text-2xl font-bold text-gray-800">About the AI Medical Diagnosis Game</h2>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-700">Advanced AI Medical Training</h3>
                  <p className="text-gray-600 mb-4">
                    Our Google Gemini AI-powered medical diagnosis game helps medical students, healthcare professionals,
                    and curious learners practice their diagnostic skills in a safe, interactive environment. Each game
                    presents a unique case with realistic symptoms and test results powered by Google's advanced AI
                    technology.
                  </p>
                  <h3 className="text-xl font-semibold mb-3 text-gray-700">Intelligent Patient Simulation</h3>
                  <p className="text-gray-600">
                    The Google AI patient responds authentically to your questions, providing symptoms and test results that
                    match real medical conditions. This creates an immersive learning experience that mirrors actual
                    clinical encounters with the power of Google's Gemini AI model.
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-700">Key Features</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>25+ different medical conditions to diagnose</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>Google Gemini AI-powered patient responses</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>Interactive patient questioning system</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>Realistic medical test ordering and results</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>20-question challenge format</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>Immediate feedback on diagnosis accuracy</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>

      <footer className="bg-gray-800 text-white py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-300">
            © 2024 Guess Disease Game. Educational medical diagnosis training powered by Google Gemini AI.
          </p>
          <p className="text-gray-400 text-sm mt-2">
            This game is for educational purposes only and should not be used for actual medical diagnosis.
          </p>
        </div>
      </footer>
    </div>
  )
}
