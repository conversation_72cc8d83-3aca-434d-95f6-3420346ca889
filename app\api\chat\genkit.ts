import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';

// 只在本地开发环境设置代理
if (process.env.NODE_ENV === 'development') {
  process.env.HTTP_PROXY = 'http://127.0.0.1:10808';
  process.env.HTTPS_PROXY = 'http://127.0.0.1:10808';
}

export const ai = genkit({
  plugins: [googleAI({
    apiKey: process.env.GOOGLE_GENAI_API_KEY,
  })],
});

export const menuSuggestionFlow = ai.defineFlow(
  {
    name: 'menuSuggestionFlow',
    inputSchema: z.object({
        message: z.string(),
        disease: z.string(),
        gameContext: z.string(),
    }),
    outputSchema: z.string(),
    streamSchema: z.string(),
  },
  async (input, { sendChunk }) => {
    const prompt = `You are a patient with ${input.disease}. The doctor is trying to diagnose you in a medical guessing game.

Rules:
- Answer questions about symptoms honestly based on ${input.disease}
- If asked about tests/examinations, provide realistic results for ${input.disease}
- Keep responses SHORT (1-2 sentences max)
- Don't reveal the disease name directly
- Act like a real patient would - sometimes uncertain, describing symptoms in everyday language
- If the doctor asks for specific tests, provide results that would be consistent with ${input.disease}

Game context: ${input.gameContext}
Doctor's question: "${input.message}"

Respond as the patient:`

    const { stream, response } = ai.generateStream({
        model: 'googleai/gemini-2.0-flash-exp',
        prompt: prompt,
        config: {
            maxOutputTokens: 100,
            temperature: 0.7,
        },
    });

    for await (const chunk of stream) {
      if (chunk.text) {
        sendChunk(chunk.text);
      }
    }

    const finalResponse = await response;
    return finalResponse.text;
  }
);

export const initialPatientMessageFlow = ai.defineFlow(
  {
    name: 'initialPatientMessageFlow',
    inputSchema: z.object({
        disease: z.string(),
    }),
    outputSchema: z.string(),
  },
  async (input) => {
    const prompt = `You are a patient with ${input.disease} who just walked into a doctor's office. Generate an initial complaint/opening statement that a patient with this condition would naturally say. 

Rules:
- Describe initial symptoms in everyday language (not medical terms)
- Sound worried/concerned but not overly dramatic
- Don't reveal the exact disease name
- Keep it conversational and realistic (2-3 sentences max)
- Start with something like "Hello doctor" or "Doctor, I'm not feeling well"
- Include 1-2 main symptoms that would bring someone to see a doctor

Generate the patient's opening statement:`

    const { text } = await ai.generate({
        model: 'googleai/gemini-2.0-flash-exp',
        prompt: prompt,
        config: {
            maxOutputTokens: 80,
            temperature: 0.8,
        },
    });

    return text;
  }
);
